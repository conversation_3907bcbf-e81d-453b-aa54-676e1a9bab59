 <!-- krypton byte -->
<!doctype html>
<html lang="en">
  <head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css" integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <title>Tiktok Downloader</title>
  </head>
  <body>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <div class="col-lg-6 offset-lg-3 " id='frm'>
        <div class="row justify-content-center">
          <div class="col-12" id="wrapMode">
            <i id="mode"></i>
          </div>
            <img src="{{ url_for('static', filename='tiktok.svg')}}" id="logo">
            <input type="text" class="form-control" placeholder="Tiktok Url" id="tiktokurl">
            <button class="btn btn-outline-primary my-1" id="submitter">Download</button>
        </div>
        <div class="emptydiv">

        </div>
    </div>
    <footer>
      <p class="copy" align="center"><a href='https://github.com/krypton-byte/tiktok-downloader'>@krypton-byte</a></p>
    </footer>
    <div class="modal"><!-- Place at bottom of page --></div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"></script>
    <script src="{{ url_for('static', filename='script.js')}}"></script>
    <style>
      /* Start by setting display:none to make this hidden.
      Then we position it in relation to the viewport window
      with position:fixed. Width, height, top and left speak
      for themselves. Background we set to 80% white with
      our animation centered, and no-repeating */
    .modal {
        display:    none;
        position:   fixed;
        z-index:    1000;
        top:        0;
        left:       0;
        height:     100%;
        width:      100%;
        background: rgba( 255, 255, 255, .8 ) 
                    url("{{ url_for('static', filename='loading.svg')}}") 
                    50% 50% 
                    no-repeat;
    }
    /* When the body has the loading class, we turn
      the scrollbar off with overflow:hidden */
    body.loading .modal {
        overflow: hidden;   
    }
    #mode {
      position: absolute;
      right: 20px;
      top: 20px;
      font-size: 30px;
    }
    /* Anytime the body has the loading class, our
      modal element will be visible */
    body.loading .modal {
        display: block;
    }
    #frm {
        padding-left: 50px;
        padding-right: 50px;
    }
    #downgrup {
        padding-top: 100px;
    }
    footer {
      position: relative;
      height: 400px;
      width: 100%;
    }
    footer > .copy {
        position: absolute;
        width: 100%;
        line-height: 40px;
        text-align: center;
        bottom:0;
    }
    </style>
  </body>
</html>