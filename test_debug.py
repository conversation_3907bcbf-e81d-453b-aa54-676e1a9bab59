# Working solution for TikTok downloads
from tiktok_downloader import snaptik, tikwm

url = 'https://www.tiktok.com/@rubyxxspam2/video/7537003679933697282'

print("=== WORKING SOLUTION ===")
print("The VideoInfo.get_info() method is currently broken because TikTok's API")
print("is returning empty responses. However, the download services still work!")
print()

print("Using snaptik service:")
try:
    downloads = snaptik(url)
    print(f"✅ Success! Found {len(downloads)} download options:")
    for i, download in enumerate(downloads):
        print(f"  {i+1}. {download}")

    # Example: Download the first video
    if downloads:
        print(f"\nTo download the first video:")
        print(f"downloads[0].download('video.mp4')")

except Exception as e:
    print(f"❌ Error: {e}")

print("\n" + "="*50)

print("Using tikwm service:")
try:
    downloads = tikwm(url)
    print(f"✅ Success! Found {len(downloads)} download options:")
    for i, download in enumerate(downloads):
        print(f"  {i+1}. {download}")

except Exception as e:
    print(f"❌ Error: {e}")

print("\n" + "="*50)
print("RECOMMENDATION:")
print("Use snaptik() or tikwm() instead of VideoInfo.get_info()")
print("Both services are working and provide download links.")
